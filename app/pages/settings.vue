<script setup lang="ts">
const { theme, setTheme } = useTheme();

const themeOptions = [
    { value: 'system', label: 'System', icon: 'i-lucide-monitor' },
    { value: 'light', label: 'Light', icon: 'i-lucide-sun' },
    { value: 'dark', label: 'Dark', icon: 'i-lucide-moon' },
] as const;
</script>

<template>
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                Settings
            </h1>

            <div class="space-y-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                            <UIcon
                                name="i-lucide-user"
                                class="h-5 w-5 mr-2"
                            />
                            Profile Settings
                        </h2>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Manage your profile information and preferences.
                        </p>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Display Name
                                </label>
                                <input
                                    type="text"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    placeholder="Enter your name"
                                >
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Email
                                </label>
                                <input
                                    type="email"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    placeholder="Enter your email"
                                >
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                            <UIcon
                                name="i-lucide-palette"
                                class="h-5 w-5 mr-2"
                            />
                            Appearance
                        </h2>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Customize the appearance of the application.
                        </p>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                    Theme
                                </label>
                                <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
                                    <div
                                        v-for="option in themeOptions"
                                        :key="option.value"
                                        class="relative"
                                    >
                                        <input
                                            :id="option.value"
                                            :value="option.value"
                                            :checked="theme === option.value"
                                            type="radio"
                                            name="theme"
                                            class="sr-only"
                                            @change="setTheme(option.value)"
                                        >
                                        <label
                                            :for="option.value"
                                            class="flex items-center justify-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                                            :class="[
                                                theme === option.value
                                                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                                                    : 'border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300',
                                            ]"
                                        >
                                            <UIcon
                                                :name="option.icon"
                                                class="h-5 w-5 mr-2"
                                            />
                                            {{ option.label }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                            <UIcon
                                name="i-lucide-bell"
                                class="h-5 w-5 mr-2"
                            />
                            Notification Settings
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                        Email Notifications
                                    </h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        Receive notifications via email
                                    </p>
                                </div>
                                <UToggle />
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                        Push Notifications
                                    </h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        Receive push notifications in browser
                                    </p>
                                </div>
                                <UToggle />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
